 WARN  Unsupported engine: wanted: {"node":"20.19.2"} (current: {"node":"v22.14.0","pnpm":"10.8.1"})

> cubent@0.30.0 check-types C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\src
> tsc --noEmit

activate/registerCommands.ts(144,2): error TS2353: Object literal may only specify known properties, and 'toggleAutoApprove' does not exist in type 'Record<"newTask" | "activationCompleted" | "plusButtonClicked" | "promptsButtonClicked" | "mcpButtonClicked" | "historyButtonClicked" | "popoutButtonClicked" | "accountButtonClicked" | ... 8 more ... | "acceptInput", any>'.
activate/registerCommands.ts(155,46): error TS2339: Property 'autoApprovalEnabled' does not exist on type 'Promise<{ apiConfiguration: { reasoningEffort?: "low" | "medium" | "high" | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | "vscode-lm" | ... 13 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }; ... 70 more ...; codebase...'.
activate/registerCommands.ts(171,19): error TS2339: Property 'updateState' does not exist on type 'ClineProvider'.
core/config/ProviderSettingsManager.ts(86,6): error TS2353: Object literal may only specify known properties, and 'anthropicApiKey' does not exist in type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(93,6): error TS2353: Object literal may only specify known properties, and 'anthropicApiKey' does not exist in type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(99,6): error TS2353: Object literal may only specify known properties, and 'anthropicApiKey' does not exist in type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(105,6): error TS2353: Object literal may only specify known properties, and 'anthropicApiKey' does not exist in type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(111,6): error TS2353: Object literal may only specify known properties, and 'anthropicApiKey' does not exist in type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(117,6): error TS2353: Object literal may only specify known properties, and 'anthropicApiKey' does not exist in type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(221,5): error TS1117: An object literal cannot have multiple properties with the same name.
core/config/ProviderSettingsManager.ts(227,5): error TS1117: An object literal cannot have multiple properties with the same name.
core/config/ProviderSettingsManager.ts(660,5): error TS1117: An object literal cannot have multiple properties with the same name.
core/config/ProviderSettingsManager.ts(666,5): error TS1117: An object literal cannot have multiple properties with the same name.
core/config/ProviderSettingsManager.ts(788,16): error TS2339: Property 'anthropicApiKey' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(798,16): error TS2339: Property 'geminiBaseUrl' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(800,16): error TS2339: Property 'xaiBaseUrl' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(824,42): error TS2339: Property 'anthropicApiKey' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(842,41): error TS2339: Property 'geminiBaseUrl' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(845,74): error TS2339: Property 'xaiBaseUrl' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(921,97): error TS2339: Property 'anthropicApiKey' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(925,47): error TS2339: Property 'anthropicApiKey' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(930,101): error TS2339: Property 'anthropicApiKey' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/task/Task.ts(378,42): error TS2341: Property 'getGlobalState' is private and only accessible within class 'ClineProvider'.
core/task/Task.ts(1441,44): error TS2339: Property 'apiConfigName' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | "vscode-lm" | "lmstudio" | ... 12 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/task/Task.ts(1449,44): error TS2345: Argument of type '{ modelId: string; cubentUnitsUsed: number; tokensUsed: number; inputTokens: number; outputTokens: number; costAccrued: number; requestsMade: number; sessionId: string; metadata: { provider: string; ... 4 more ...; timestamp: number; }; }' is not assignable to parameter of type 'CubentUsageEntry'.
  Property 'userId' is missing in type '{ modelId: string; cubentUnitsUsed: number; tokensUsed: number; inputTokens: number; outputTokens: number; costAccrued: number; requestsMade: number; sessionId: string; metadata: { provider: string; ... 4 more ...; timestamp: number; }; }' but required in type 'CubentUsageEntry'.
core/user/UsageTrackingService.ts(4,42): error TS2307: Cannot find module '@shared/api' or its corresponding type declarations.
core/user/UserManagementIntegration.ts(3,42): error TS2307: Cannot find module '@shared/api' or its corresponding type declarations.
core/user/UserManagementIntegration.ts(7,29): error TS2307: Cannot find module '../../packages/cloud/src/AuthService' or its corresponding type declarations.
core/user/UserManagementService.ts(16,29): error TS2307: Cannot find module '../../packages/cloud/src/AuthService' or its corresponding type declarations.
core/user/UserManagementService.ts(70,24): error TS2554: Expected 0 arguments, but got 1.
core/webview/ClineProvider.ts(169,26): error TS2554: Expected 2 arguments, but got 1.
core/webview/ClineProvider.ts(675,31): error TS2345: Argument of type '[ExtensionMessage]' is not assignable to parameter of type 'never'.
core/webview/webviewMessageHandler.ts(1496,46): error TS2341: Property 'usageTrackingService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1524,45): error TS2341: Property 'trialManagementService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1553,47): error TS2341: Property 'userManagementService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1597,27): error TS2341: Property 'userManagementService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1599,47): error TS2341: Property 'userManagementService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1612,42): error TS2341: Property 'trialManagementService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1613,38): error TS2322: Type '"trialExtended"' is not assignable to type '"autoApprovalEnabled" | "browserToolEnabled" | "remoteBrowserEnabled" | "maxReadFileLine" | "codebaseIndexConfig" | "action" | "showHumanRelayDialog" | "acceptInput" | "state" | ... 44 more ... | "messageUsageData"'.
core/webview/webviewMessageHandler.ts(1615,45): error TS2341: Property 'trialManagementService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1628,27): error TS2341: Property 'trialManagementService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1639,8): error TS2678: Type '"showUsageDetails"' is not comparable to type '"currentApiConfigName" | "customInstructions" | "condensingApiConfigId" | "autoApprovalEnabled" | "alwaysAllowReadOnly" | "alwaysAllowReadOnlyOutsideWorkspace" | "alwaysAllowWrite" | ... 147 more ... | "getMessageUsageData"'.
core/webview/webviewMessageHandler.ts(1898,9): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
core/webview/webviewMessageHandler.ts(1938,9): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
core/webview/webviewMessageHandler.ts(2024,7): error TS2353: Object literal may only specify known properties, and 'messageTs' does not exist in type 'ExtensionMessage'.
core/webview/webviewMessageHandler.ts(2083,6): error TS2353: Object literal may only specify known properties, and 'messageTs' does not exist in type 'ExtensionMessage'.
core/webview/webviewMessageHandler.ts(2091,6): error TS2353: Object literal may only specify known properties, and 'messageTs' does not exist in type 'ExtensionMessage'.
extension.ts(261,29): error TS2339: Property 'removeAllListeners' does not exist on type 'UserManagementIntegration'.
services/AuthenticationService.ts(220,5): error TS2353: Object literal may only specify known properties, and 'updatedAt' does not exist in type 'UserSession'.
services/AuthenticationService.ts(287,29): error TS2551: Property 'pictureUrl' does not exist on type 'CubentUser'. Did you mean 'picture'?
services/AuthenticationService.ts(304,31): error TS2339: Property 'trialEndDate' does not exist on type 'CubentUser'.
services/CubentWebDatabaseService.ts(95,35): error TS2559: Type 'Pool' has no properties in common with type 'PoolConfig'.
services/CubentWebDatabaseService.ts(98,37): error TS2353: Object literal may only specify known properties, and 'adapter' does not exist in type 'Subset<PrismaClientOptions, PrismaClientOptions>'.
services/CubentWebDatabaseService.ts(138,4): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/CubentWebDatabaseService.ts(139,4): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/CubentWebDatabaseService.ts(144,4): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/CubentWebDatabaseService.ts(147,4): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/CubentWebDatabaseService.ts(168,4): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/CubentWebDatabaseService.ts(169,4): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/CubentWebDatabaseService.ts(174,4): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/CubentWebDatabaseService.ts(177,4): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/CubentWebDatabaseService.ts(284,4): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/CubentWebDatabaseService.ts(285,4): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/CubentWebDatabaseService.ts(286,4): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(113,35): error TS2559: Type 'Pool' has no properties in common with type 'PoolConfig'.
services/NeonDatabaseService.ts(117,5): error TS2322: Type 'PrismaNeon' is not assignable to type 'never'.
services/NeonDatabaseService.ts(171,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(172,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(173,27): error TS2339: Property 'extensionApiKey' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(174,24): error TS2339: Property 'sessionToken' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(175,29): error TS2339: Property 'lastExtensionSync' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(176,28): error TS2339: Property 'lastSettingsSync' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(178,5): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/NeonDatabaseService.ts(180,27): error TS2551: Property 'termsAcceptedAt' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'. Did you mean 'termsAccepted'?
services/NeonDatabaseService.ts(185,5): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/NeonDatabaseService.ts(186,29): error TS2339: Property 'extensionSettings' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(187,23): error TS2339: Property 'preferences' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(212,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(213,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(214,27): error TS2339: Property 'extensionApiKey' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(215,24): error TS2339: Property 'sessionToken' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(216,29): error TS2339: Property 'lastExtensionSync' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(217,28): error TS2339: Property 'lastSettingsSync' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(219,5): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/NeonDatabaseService.ts(221,27): error TS2551: Property 'termsAcceptedAt' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'. Did you mean 'termsAccepted'?
services/NeonDatabaseService.ts(226,5): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/NeonDatabaseService.ts(227,29): error TS2339: Property 'extensionSettings' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(228,23): error TS2339: Property 'preferences' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(268,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(269,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(270,27): error TS2339: Property 'extensionApiKey' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(271,24): error TS2339: Property 'sessionToken' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(272,29): error TS2339: Property 'lastExtensionSync' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(273,28): error TS2339: Property 'lastSettingsSync' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(275,5): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/NeonDatabaseService.ts(277,27): error TS2551: Property 'termsAcceptedAt' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'. Did you mean 'termsAccepted'?
services/NeonDatabaseService.ts(282,5): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/NeonDatabaseService.ts(283,29): error TS2339: Property 'extensionSettings' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(284,23): error TS2339: Property 'preferences' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(302,6): error TS2353: Object literal may only specify known properties, and 'inputTokens' does not exist in type 'Without<UsageAnalyticsCreateInput, UsageAnalyticsUncheckedCreateInput> & UsageAnalyticsUncheckedCreateInput'.
services/NeonDatabaseService.ts(398,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(399,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(400,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/UsageTrackingService.ts(135,45): error TS2339: Property 'cacheReadTokens' does not exist on type 'TokenUsage'.
services/UsageTrackingService.ts(136,46): error TS2339: Property 'cacheWriteTokens' does not exist on type 'TokenUsage'.
services/UsageTrackingService.ts(162,16): error TS2304: Cannot find name 'usageEntry'.
services/UsageTrackingService.ts(288,66): error TS2345: Argument of type '"warning" | "critical" | "exceeded"' is not assignable to parameter of type '"warning" | "exceeded" | "reset"'.
  Type '"critical"' is not assignable to type '"warning" | "exceeded" | "reset"'.
 ELIFECYCLE  Command failed with exit code 2.
