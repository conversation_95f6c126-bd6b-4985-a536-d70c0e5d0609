
> @cubent/vscode-webview@ build C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui
> tsc -b && vite build

src/components/chat/ChatView.tsx(1431,9): error TS2322: Type '{ ref: RefObject<HTMLTextAreaElement>; inputValue: string; setInputValue: Dispatch<SetStateAction<string>>; sendingDisabled: boolean; ... 21 more ...; onModelSettingsClick: () => void; }' is not assignable to type 'IntrinsicAttributes & ChatTextAreaProps & RefAttributes<HTMLTextAreaElement>'.
  Property 'isWelcomeState' does not exist on type 'IntrinsicAttributes & ChatTextAreaProps & RefAttributes<HTMLTextAreaElement>'.
 ELIFECYCLE  Command failed with exit code 1.
