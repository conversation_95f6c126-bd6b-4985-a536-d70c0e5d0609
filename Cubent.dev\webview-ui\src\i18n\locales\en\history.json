{"recentTasks": "Chats", "viewAll": "View All Chats", "tokens": "Tokens: ↑{{in}} ↓{{out}}", "cache": "Cache: +{{writes}} → {{reads}}", "apiCost": "API Cost: ${{cost}}", "history": "History", "exitSelectionMode": "Exit Selection Mode", "enterSelectionMode": "Enter Selection Mode", "done": "Done", "searchPlaceholder": "Fuzzy search history...", "newest": "Newest", "oldest": "Oldest", "mostExpensive": "Most Expensive", "mostTokens": "Most Tokens", "mostRelevant": "Most Relevant", "deleteChatTitle": "Delete Chat (<PERSON><PERSON> + <PERSON>lick to skip confirmation)", "tokensLabel": "Tokens:", "cacheLabel": "Cache:", "apiCostLabel": "API Cost:", "copyPrompt": "Copy Prompt", "exportChat": "Export <PERSON>", "deleteChat": "Delete Chat", "showUsageStats": "Show Usage Statistics", "deleteChatMessage": "Are you sure you want to delete this chat? This action cannot be undone.", "cancel": "Cancel", "delete": "Delete", "exitSelection": "Exit Selection", "selectionMode": "Selection Mode", "deselectAll": "Deselect all", "selectAll": "Select all", "selectedItems": "Selected {{selected}}/{{total}} items", "clearSelection": "Clear Selection", "deleteSelected": "Delete Selected", "deleteChats": "Delete Chats", "confirmDeleteChats": "Are you sure you want to delete {{count}} chats?", "deleteChatsWarning": "Deleted chats cannot be recovered. Please make sure you want to proceed.", "deleteItems": "Delete {{count}} Items", "showAllWorkspaces": "Show chats from all workspaces"}